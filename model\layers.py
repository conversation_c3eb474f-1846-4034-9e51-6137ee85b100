import torch
import torch.nn.functional as F
from torch.nn.functional import scaled_dot_product_attention
from torch import nn
import math
from torch import Tensor
from einops import rearrange

class Conv2dWithConstraint(nn.Conv2d):
    def __init__(self, *args, max_norm=1.5, **kwargs):
        super(Conv2dWithConstraint, self).__init__(*args, **kwargs)
        self.max_norm = max_norm
        nn.init.kaiming_normal_(self.weight, mode='fan_in', nonlinearity='linear')

    def forward(self, x):
        self.weight.data = torch.renorm(
            self.weight.data, p=2, dim=0, maxnorm=self.max_norm
        )
        return super(Conv2dWithConstraint, self).forward(x)


class LazyLinearWithConstraint(nn.LazyLinear):
    def __init__(self, *args, max_norm=1., **kwargs):
        super(LazyLinearWithConstraint, self).__init__(*args, **kwargs)
        self.max_norm = max_norm

    def forward(self, x):
        self.weight.data = torch.renorm(
            self.weight.data, p=2, dim=0, maxnorm=self.max_norm
        )
        return nn.LazyLinear.forward(self, x)


class LinearWithConstraint(nn.Linear):
    def __init__(self, *config, max_norm=1, **kwconfig):
        self.max_norm = max_norm
        super(LinearWithConstraint, self).__init__(*config, **kwconfig)
        nn.init.kaiming_normal_(self.weight, mode='fan_in', nonlinearity='linear')

    def forward(self, x):
        self.weight.data = torch.renorm(
            self.weight.data, p=2, dim=0, maxnorm=self.max_norm
        )
        return super(LinearWithConstraint, self).forward(x)


class PositionalEncodingFourier(nn.Module):
    def __init__(self, hidden_dim=16, dim=16, temperature=10000):
        super().__init__()
        self.token_projection = nn.Conv2d(hidden_dim * 2, dim, kernel_size=1)
        self.scale = 2 * math.pi
        self.temperature = temperature
        self.hidden_dim = hidden_dim
        self.dim = dim

    def forward(self, B, H, W):
        mask = torch.zeros(B, H, W).bool().to(self.token_projection.weight.device)
        not_mask = ~mask
        y_embed = not_mask.cumsum(1, dtype=torch.float32)
        x_embed = not_mask.cumsum(2, dtype=torch.float32)
        eps = 1e-6
        y_embed = y_embed / (y_embed[:, -1:, :] + eps) * self.scale
        x_embed = x_embed / (x_embed[:, :, -1:] + eps) * self.scale

        dim_t = torch.arange(self.hidden_dim, dtype=torch.float32, device=mask.device)
        dim_t = self.temperature ** (2 * (dim_t // 2) / self.hidden_dim)

        pos_x = x_embed[:, :, :, None] / dim_t
        pos_y = y_embed[:, :, :, None] / dim_t
        pos_x = torch.stack((pos_x[:, :, :, 0::2].sin(),
                             pos_x[:, :, :, 1::2].cos()), dim=4).flatten(3)
        pos_y = torch.stack((pos_y[:, :, :, 0::2].sin(),
                             pos_y[:, :, :, 1::2].cos()), dim=4).flatten(3)
        pos = torch.cat((pos_y, pos_x), dim=3).permute(0, 3, 1, 2)
        pos = self.token_projection(pos)

        return pos


class LayerNorm(nn.Module):
    def __init__(self, normalized_shape=None, eps=1e-6, data_format="channels_last"):
        super().__init__()
        self.eps = eps
        self.data_format = data_format
        self.weight = None
        self.bias = None

    def forward(self, x):
        if self.weight is None:  # Dynamic initialization
            if self.data_format == "channels_last":
                normalized_shape = x.shape[-1]
            else:
                # Handle both 3D and 4D inputs for channels_first
                if x.dim() == 4:
                    normalized_shape = x.shape[1]  # [B, C, H, W]
                else:  # 3D input [B, N, C] treated as channels_last
                    normalized_shape = x.shape[-1]  # Use last dimension (C)

            self.weight = nn.Parameter(torch.ones(normalized_shape)).to(x.device)
            self.bias = nn.Parameter(torch.zeros(normalized_shape)).to(x.device)

        if self.data_format == "channels_last":
            return F.layer_norm(x, (self.weight.shape[0],), self.weight, self.bias, self.eps)
        else:
            if x.dim() == 4:
                # 4D input: [B, C, H, W]
                u = x.mean(1, keepdim=True)
                s = (x - u).pow(2).mean(1, keepdim=True)
                x = (x - u) / torch.sqrt(s + self.eps)
                x = self.weight.view(1, -1, 1, 1) * x + self.bias.view(1, -1, 1, 1)
            else:
                # 3D input: [B, N, C], normalize over N (dim=1)
                u = x.mean(1, keepdim=True)
                s = (x - u).pow(2).mean(1, keepdim=True)
                x = (x - u) / torch.sqrt(s + self.eps)
                # Reshape weight/bias to (1, 1, C) for correct broadcasting
                x = self.weight.view(1, 1, -1) * x + self.bias.view(1, 1, -1)
            return x


class LocalSpatialAttention(nn.Module):
    def __init__(self, in_channels, kernel_size):
        super().__init__()

        # if self.norm_type == 'BN':
        #     norm_layer = nn.BatchNorm2d(in_channels)
        # else:  # 默认使用 LayerNorm
        #     norm_layer = LayerNorm(in_channels, data_format="channels_first")

        self.dynamic_conv = nn.Sequential(
            nn.Conv2d(in_channels, in_channels,
                     kernel_size=(1, kernel_size),
                     padding=(0, kernel_size//2),
                     groups=in_channels),
            # LayerNorm(in_channels, data_format="channels_first"),
            nn.BatchNorm2d(in_channels),
            nn.ELU(),
            nn.Dropout(0.3),
            nn.Conv2d(in_channels, in_channels,
                     kernel_size=(1, kernel_size),
                     padding=(0, kernel_size//2)),
            nn.Sigmoid()
        )

    def forward(self, x):
        return x * self.dynamic_conv(x)


class Mish(nn.Module):
    def __init__(self):
        super().__init__()

    def forward(self, x):
        return x * torch.tanh(F.softplus(x))

class MultiHeadAttention(nn.Module):
    def __init__(self, emb_size, num_heads, dropout):
        super().__init__()
        self.emb_size = emb_size
        self.num_heads = num_heads
        self.keys = nn.Linear(emb_size, emb_size)
        self.queries = nn.Linear(emb_size, emb_size)
        self.values = nn.Linear(emb_size, emb_size)
        self.att_drop = nn.Dropout(dropout)
        self.projection = nn.Linear(emb_size, emb_size)

    def forward(self, x: Tensor, mask: Tensor = None) -> Tensor:
        queries = rearrange(self.queries(x), "b n (h d) -> b h n d", h=self.num_heads)
        keys = rearrange(self.keys(x), "b n (h d) -> b h n d", h=self.num_heads)
        values = rearrange(self.values(x), "b n (h d) -> b h n d", h=self.num_heads)
        energy = torch.einsum('bhqd, bhkd -> bhqk', queries, keys)
        if mask is not None:
            fill_value = torch.finfo(torch.float32).min
            energy.mask_fill(~mask, fill_value)

        scaling = self.emb_size ** (1 / 2)
        att = F.softmax(energy / scaling, dim=-1)
        att = self.att_drop(att)
        out = torch.einsum('bhal, bhlv -> bhav ', att, values)
        out = rearrange(out, "b h n d -> b n (h d)")
        out = self.projection(out)
        return out


class EfficientAttention(nn.Module):
    """基于 PyTorch 2.0 的高效多头注意力"""
    def __init__(self, embed_dim, num_heads, dropout=0.3):
        super().__init__()
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        self.qkv = nn.Linear(embed_dim, 3 * embed_dim)
        self.dropout = dropout

    def forward(self, x):
        B, N, C = x.shape
        qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, self.head_dim).permute(2, 0, 3, 1, 4)
        q, k, v = qkv.unbind(0)
        attn_out = scaled_dot_product_attention(q, k, v, dropout_p=self.dropout)
        return attn_out.permute(0, 2, 1, 3).reshape(B, N, C)

class UnifiedAttention1(nn.Module):
    """统一注意力模块（支持通道c/空间s/频率f/多头m/频率-空间fs模式）"""

    def __init__(self, mode='csfm', in_channels=256, num_heads=8, num_bands=6):
        super().__init__()
        self.mode = mode.lower()
        self.gates = nn.ParameterDict()

        # ---- 通道注意力 ----
        if 'c' in self.mode:
            self.gates['c'] = nn.Parameter(torch.ones(1))
            self.channel_att = nn.Sequential(
                nn.AdaptiveAvgPool2d(1),
                Conv2dWithConstraint(in_channels, max(8, in_channels // 16), 1),
                nn.ELU(),
                Conv2dWithConstraint(max(8, in_channels // 16), in_channels, 1),
                nn.Sigmoid()
            )

        # ---- 空间注意力 ----
        if 's' in self.mode:
            self.gates['s'] = nn.Parameter(torch.ones(1))
            self.spatial_att = nn.Sequential(
                Conv2dWithConstraint(in_channels, in_channels, (3, 3), padding=1, groups=in_channels),
                LayerNorm(in_channels, data_format="channels_first"),
                nn.ELU(),
                Conv2dWithConstraint(in_channels, 1, 1),
                nn.Sigmoid()
            )

        # ---- 频率注意力 ----
        if 'f' in self.mode:
            self.gates['f'] = nn.Parameter(torch.ones(1))
            self.band_weights = nn.Parameter(torch.ones(1, 1, num_bands))
            self.num_bands = num_bands

        # ---- 高效多头注意力 ----
        if 'm' in self.mode:
            self.gates['m'] = nn.Parameter(torch.ones(1))
            self.mha = EfficientAttention(
                embed_dim=in_channels,
                num_heads=num_heads,
                dropout=0.3
            )
            self.norm = LayerNorm(in_channels, data_format="channels_last")

        # ---- 频率-空间联合注意力 ----
        if 'fs' in self.mode:
            self.gates['fs'] = nn.Parameter(torch.ones(1))
            self.fs_att = nn.Sequential(
                Conv2dWithConstraint(in_channels, in_channels, (3, 3), padding=1),
                LayerNorm(in_channels, data_format="channels_first"),
                nn.ELU(),
                Conv2dWithConstraint(in_channels, in_channels, 1),
                nn.Sigmoid()
            )

    def forward(self, x):
        identity = x

        # ---- 通道注意力 ----
        if 'c' in self.mode:
            x = x * self.channel_att(x) * self.gates['c']

        # ---- 空间注意力 ----
        if 's' in self.mode:
            x = x * self.spatial_att(x) * self.gates['s']

        # ---- 频率注意力 ----
        if 'f' in self.mode:
            B, _, C, T = x.shape
            band_size = T // self.num_bands
            weighted = torch.zeros_like(x)
            for i in range(self.num_bands):
                start = i * band_size
                end = (i + 1) * band_size if i < self.num_bands - 1 else T
                weight = F.softmax(self.band_weights[:, :, i], dim=-1)
                weighted[:, :, :, start:end] = x[:, :, :, start:end] * weight.unsqueeze(-1) * self.gates['f']
            x = weighted

        # ---- 多头注意力 ----
        if 'm' in self.mode:
            B, C, H, W = x.shape
            # x_reshaped = x.permute(0, 2, 3, 1).reshape(B, H * W, C)
            x_reshaped = x.reshape(B, C, H * W).permute(0, 2, 1)
            x_norm = self.norm(x_reshaped)
            attn_out = self.mha(x_norm)
            x = attn_out.reshape(B, H, W, C).permute(0, 3, 1, 2) * self.gates['m']


        if 'fs' in self.mode:
            fs_weight = self.fs_att(x) * self.gates['fs']
            x = x * fs_weight

        return identity + x

class UnifiedAttention(nn.Module):
    """统一注意力模块（支持通道c/空间s/频率f/多头m/频率-空间fs模式）"""

    def __init__(self, mode='csfm', in_channels=256, num_heads=8, num_bands=6):
        super().__init__()
        self.mode = mode.lower()
        self.gates = nn.ParameterDict()

        # ---- 通道注意力 ----
        if 'c' in self.mode:
            self.gates['c'] = nn.Parameter(torch.ones(1))
            self.channel_att = nn.Sequential(
                nn.AdaptiveAvgPool2d(1),
                nn.Conv2d(in_channels, max(8, in_channels // 16), 1),
                nn.ELU(),
                # nn.Dropout(0.1),
                nn.Conv2d(max(8, in_channels // 16), in_channels, 1),
                nn.Sigmoid()
            )

        if 's' in self.mode:
            self.gates['s'] = nn.Parameter(torch.ones(1))
            self.spatial_att = nn.Sequential(
                nn.Conv2d(in_channels, in_channels, (3, 3), padding=1, groups=in_channels),
                LayerNorm(in_channels, data_format="channels_first"),
                # nn.BatchNorm2d(in_channels),
                nn.ELU(),
                # nn.Dropout(0.1),
                nn.Conv2d(in_channels, 1, 1),
                nn.Sigmoid()
            )

        if 'm' in self.mode:
            self.gates['m'] = nn.Parameter(torch.ones(1))
            self.mha = MultiHeadAttention(
                emb_size=in_channels,
                num_heads=num_heads,
                dropout=0.1,
            )
            self.norm = LayerNorm(in_channels, data_format="channels_last")

        # ---- 频率注意力 ----
        if 'f' in self.mode:
            self.gates['f'] = nn.Parameter(torch.ones(1))
            self.band_weights = nn.Parameter(torch.ones(1, 1, num_bands))
            self.num_bands = num_bands

        # if 'c' in self.mode:
        #     # 使用 UNet 风格的通道注意力
        #     avg_out = self.fc2(self.relu(self.fc1(self.avg_pool(x))))
        #     max_out = self.fc2(self.relu(self.fc1(self.max_pool(x))))
        #     channel_attention = self.sigmoid(avg_out + max_out)
        #     x = x * channel_attention * self.gates['c']
        #
        # if 's' in self.mode:
        #     # 使用 UNet 风格的空间注意力
        #     avg_out = torch.mean(x, dim=1, keepdim=True)
        #     max_out, _ = torch.max(x, dim=1, keepdim=True)
        #     spatial_features = torch.cat([avg_out, max_out], dim=1)
        #     spatial_attention = self.sigmoid(self.conv1(spatial_features))
        #     x = x * spatial_attention * self.gates['s']

    def forward(self, x):
        identity = x

        # ---- 通道注意力 ----
        if 'c' in self.mode:
            x = x * self.channel_att(x) * self.gates['c']

        # ---- 空间注意力 ----
        if 's' in self.mode:
            x = x * self.spatial_att(x) * self.gates['s']

        # ---- 频率注意力 ----
        if 'f' in self.mode:
            B, _, C, T = x.shape
            band_size = T // self.num_bands
            weighted = torch.zeros_like(x)
            for i in range(self.num_bands):
                start = i * band_size
                end = (i + 1) * band_size if i < self.num_bands - 1 else T
                weight = F.softmax(self.band_weights[:, :, i], dim=-1)
                weighted[:, :, :, start:end] = x[:, :, :, start:end] * weight.unsqueeze(-1) * self.gates['f']
            x = weighted

        # ---- 多头注意力 ----
        if 'm' in self.mode:
            B, C, H, W = x.shape
            x_reshaped = x.reshape(B, C, H * W).permute(0, 2, 1)
            x_norm = self.norm(x_reshaped)
            attn_out = self.mha(x_norm)
            x = attn_out.reshape(B, H, W, C).permute(0, 3, 1, 2) * self.gates['m']

        return identity + x



class FrequencyBandAttention(nn.Module):
    """可学习重叠频段的动态频带注意力"""

    def __init__(self, num_bands=6, max_overlap=0.3):
        super().__init__()
        self.num_bands = num_bands
        self.overlap_ratio = nn.Parameter(torch.rand(num_bands) * max_overlap)  # 可学习重叠率
        self.mlp = nn.Sequential(
            nn.Conv1d(num_bands, num_bands * 2, 1),
            nn.ELU(),
            nn.Dropout(0.1),
            nn.Conv1d(num_bands * 2, num_bands, 1),
            nn.Softmax(dim=-1)
        )

    def forward(self, x):
        B, _, C, T = x.shape

        # 将张量转换为标量
        sum_overlap = sum([ratio.item() for ratio in self.overlap_ratio])
        band_length = T // (self.num_bands - sum_overlap)

        # 动态计算各频段区间
        positions = [0]
        for i in range(self.num_bands):
            overlap = band_length * self.overlap_ratio[i].item()
            positions.append(positions[-1] + band_length - int(overlap))

        # 频段加权（修复输入维度）
        weights_input = self.overlap_ratio.unsqueeze(0).unsqueeze(-1)  # [1, 6, 1]
        weights = self.mlp(weights_input).squeeze()  # 输出形状 [6]

        weighted_bands = []
        for i in range(self.num_bands):
            start = int(positions[i])
            end = int(positions[i + 1])
            end = min(end, T)
            band = x[:, :, :, start:end]
            weighted_bands.append(band * weights[i])

        return torch.cat(weighted_bands, dim=3)



class MultiScaleSpectralAttention(nn.Module):
    def __init__(self, in_channels, F1=16, scales=[125, 62]):
        super().__init__()
        self.branches = nn.ModuleList()
        for s in scales:
            padding = (s - 1) // 2
            self.branches.append(
                nn.Sequential(
                    nn.ConstantPad2d((padding, s - 1 - padding, 0, 0), 0),
                    nn.Conv2d(in_channels, F1, (1, s), padding=0),
                    nn.BatchNorm2d(F1),
                    nn.ELU(),
                    # nn.AdaptiveMaxPool2d((None, 750)),
                    nn.Dropout(0.3),
                    SEBlock(F1),

                )
            )

        self.fusion = nn.Sequential(
            nn.Conv2d(F1 * len(scales), F1, kernel_size=1),
            LocalSpatialAttention(F1, kernel_size=3)  # 使用修正后的注意力
        )

    def forward(self, x):
        branch_outs = [branch(x) for branch in self.branches]
        return self.fusion(torch.cat(branch_outs, dim=1))

class MultiScaleSpectralAttention1(nn.Module):
    def __init__(self, in_channels, F1=16, scales=[16, 8], reduction=4):
        super().__init__()
        self.num_scales = len(scales)
        self.F1 = F1

        # 共享的深度可分离卷积基础层
        self.shared_base = nn.Sequential(
            nn.Conv2d(in_channels, F1, kernel_size=1),  # 降维
            nn.BatchNorm2d(F1),
            nn.ELU()
        )

        # 轻量化多尺度分支（使用深度可分离卷积）
        self.scale_branches = nn.ModuleList()
        for i, scale in enumerate(scales):
            # 使用更小的有效卷积核
            effective_scale = min(scale, 16)  # 限制最大卷积核大小
            padding = (effective_scale - 1) // 2

            branch = nn.Sequential(
                # 深度可分离卷积：深度卷积 + 点卷积
                nn.Conv2d(F1, F1, (1, effective_scale),
                          padding='same' , groups=F1),  # 深度卷积
                nn.Conv2d(F1, F1 // 2, kernel_size=1),  # 点卷积降维
                nn.BatchNorm2d(F1 // 2),
                nn.ELU(),
                nn.Dropout(0.2)  # 降低dropout率
            )
            self.scale_branches.append(branch)

        # 轻量化注意力融合模块
        total_channels = (F1 // 2) * len(scales)
        self.attention_fusion = nn.Sequential(
            # 全局平均池化获取全局信息
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(total_channels, total_channels // reduction, 1),
            nn.ELU(),
            nn.Conv2d(total_channels // reduction, total_channels, 1),
            nn.Sigmoid()
        )

        # 最终融合层
        self.final_fusion = nn.Sequential(
            nn.Conv2d(total_channels, F1, kernel_size=1),
            nn.BatchNorm2d(F1),
            nn.ELU()
        )

        # 残差连接的投影层
        self.residual_proj = nn.Conv2d(in_channels, F1, kernel_size=1) if in_channels != F1 else nn.Identity()

    def forward(self, x):
        # 保存残差连接
        residual = self.residual_proj(x)

        # 共享基础特征提取
        base_features = self.shared_base(x)

        # 多尺度特征提取
        scale_features = []
        for branch in self.scale_branches:
            scale_features.append(branch(base_features))

        # 拼接多尺度特征
        concat_features = torch.cat(scale_features, dim=1)

        # 注意力加权
        attention_weights = self.attention_fusion(concat_features)
        weighted_features = concat_features * attention_weights

        # 最终融合
        output = self.final_fusion(weighted_features)

        # 残差连接
        return output + residual

    # def __init__(self, in_channels, F1=16, num_scales=3, reduction=8):
    #     super().__init__()
    #     self.num_scales = num_scales
    #     self.F1 = F1
    #
    #     # 输入投影
    #     self.input_proj = nn.Conv2d(in_channels, F1, kernel_size=1)
    #
    #     # 可学习的多尺度卷积核
    #     self.dynamic_kernels = nn.Parameter(
    #         torch.randn(num_scales, F1, 1, 7) * 0.02  # 使用较小的固定最大核大小
    #     )
    #
    #     # 批归一化和激活
    #     self.bn = nn.BatchNorm2d(F1 * num_scales)
    #     self.activation = nn.ELU()
    #     self.dropout = nn.Dropout2d(0.15)
    #
    #     # 通道注意力模块
    #     self.channel_attention = nn.Sequential(
    #         nn.AdaptiveAvgPool2d(1),
    #         nn.Conv2d(F1 * num_scales, (F1 * num_scales) // reduction, 1),
    #         nn.ELU(),
    #         nn.Conv2d((F1 * num_scales) // reduction, F1 * num_scales, 1),
    #         nn.Sigmoid()
    #     )
    #
    #     # 输出投影
    #     self.output_proj = nn.Sequential(
    #         nn.Conv2d(F1 * num_scales, F1, kernel_size=1),
    #         nn.BatchNorm2d(F1)
    #     )
    #
    #     # 残差连接
    #     self.residual_proj = nn.Conv2d(in_channels, F1, kernel_size=1) if in_channels != F1 else nn.Identity()
    #
    # def forward(self, x):
    #     B, C, H, W = x.shape
    #     residual = self.residual_proj(x)
    #
    #     # 输入投影
    #     x = self.input_proj(x)  # [B, F1, H, W]
    #
    #     # 多尺度卷积
    #     multi_scale_features = []
    #     for i in range(self.num_scales):
    #         # 动态padding以适应不同的有效核大小
    #         kernel = self.dynamic_kernels[i]  # [F1, 1, 7]
    #         kernel_size = kernel.shape[-1]
    #         padding = (kernel_size - 1) // 2
    #
    #         # 使用F.conv2d进行动态卷积
    #         feature = F.conv2d(x, kernel, padding=(0, padding), groups=self.F1)
    #         multi_scale_features.append(feature)
    #
    #     # 拼接多尺度特征
    #     concat_features = torch.cat(multi_scale_features, dim=1)  # [B, F1*num_scales, H, W]
    #
    #     # 批归一化和激活
    #     concat_features = self.activation(self.bn(concat_features))
    #     concat_features = self.dropout(concat_features)
    #
    #     # 通道注意力
    #     attention = self.channel_attention(concat_features)
    #     attended_features = concat_features * attention
    #
    #     # 输出投影
    #     output = self.output_proj(attended_features)
    #
    #     # 残差连接
    #     return output + residual

class TemporalSelfAttention(nn.Module):
    def __init__(self, in_channels, num_heads=8, dropout=0.1):
        super().__init__()
        self.num_heads = num_heads
        self.head_dim = in_channels // num_heads
        self.scale = self.head_dim ** -0.5
        self.qkv = nn.Conv1d(in_channels, in_channels * 3, kernel_size=1)
        self.proj = nn.Conv1d(in_channels, in_channels, kernel_size=1)
        self.dropout = nn.Dropout(dropout)


    def forward(self, x):
        # x: [B, C, T]
        B, C, T = x.shape
        qkv = self.qkv(x)  # [B, 3C, T]
        q, k, v = qkv.reshape(B, 3, self.num_heads, self.head_dim, T).unbind(1)
        # [B, heads, head_dim, T] -> [B, heads, T, head_dim]
        q, k, v = q.permute(0,1,3,2), k.permute(0,1,3,2), v.permute(0,1,3,2)
        attn = (q @ k.transpose(-2,-1)) * self.scale
        attn = attn.softmax(-1)
        out = (attn @ v)  # [B, heads, T, head_dim]
        out = out.permute(0,1,3,2).reshape(B, C, T)

        return self.proj(out)



# --------------------- 残差模块 ---------------------
class ResidualBlock(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size=(1, 3), dropout=0.3):
        super(ResidualBlock, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size, padding=(0, 1))
        self.bn1 = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout2d(dropout)
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size, padding=(0, 1))
        self.bn2 = nn.BatchNorm2d(out_channels)

        if in_channels != out_channels:
            self.shortcut = nn.Conv2d(in_channels, out_channels, kernel_size=1)
        else:
            self.shortcut = nn.Identity()

    def forward(self, x):
        residual = self.shortcut(x)
        out = self.relu(self.bn1(self.conv1(x)))
        out = self.dropout(out)
        out = self.bn2(self.conv2(out))

        return self.relu(out + residual)


class SEBlock(nn.Module):
    """压缩-激励模块（动态缩减率）"""

    def __init__(self, in_channels, reduction=16):
        super().__init__()
        self.se = nn.Sequential(
            nn.AdaptiveMaxPool2d(1),
            nn.Conv2d(in_channels, in_channels // reduction, 1),
            nn.BatchNorm2d(in_channels // reduction),
            nn.ELU(),
            nn.Conv2d(in_channels // reduction, in_channels, 1),
            nn.Sigmoid()
        )

    def forward(self, x):
        scale = self.se(x)
        return x * scale


class EEGResidualBlock(nn.Module):
    def __init__(self, in_channels, out_channels, dropout=0.3):
        super().__init__()

        # 多尺度卷积分支 - 修改卷积核方向
        self.conv_layers = nn.ModuleList([
            # 7x1卷积组改为1x7
            nn.Sequential(
                nn.Conv2d(in_channels, out_channels, (1, 7), padding=(0, 3)),
                nn.BatchNorm2d(out_channels),
                nn.ELU(),
                nn.Conv2d(out_channels, out_channels, (1, 7), padding=(0, 3)),
                nn.BatchNorm2d(out_channels),
                nn.ELU(),
                nn.Dropout2d(dropout)
            ),
            # 5x1卷积组改为1x5
            nn.Sequential(
                nn.Conv2d(out_channels, out_channels, (1, 5), padding=(0, 2)),
                nn.BatchNorm2d(out_channels),
                nn.ELU(),
                nn.Conv2d(out_channels, out_channels, (1, 5), padding=(0, 2)),
                nn.BatchNorm2d(out_channels),
                nn.ELU(),
                nn.Dropout2d(dropout)
            ),
            # 3x1卷积组改为1x3
            nn.Sequential(
                nn.Conv2d(out_channels, out_channels, (1, 3), padding=(0, 1)),
                nn.BatchNorm2d(out_channels),
                nn.ELU(),
                nn.Conv2d(out_channels, out_channels, (1, 3), padding=(0, 1)),
                nn.BatchNorm2d(out_channels),
                nn.ELU(),
                nn.Dropout2d(dropout)
            )
        ])

        # 跳跃连接保持不变
        self.shortcut = nn.Sequential()
        if in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, (1, 1)),
                nn.BatchNorm2d(out_channels)
            )

        # 最终激活
        self.final_activation = nn.ELU()

    def forward(self, x):
        identity = self.shortcut(x)

        for layer in self.conv_layers:
            x = layer(x)

        return self.final_activation(x + identity)


class EEGResidualBlock1(nn.Module):
    def __init__(self, in_channels, out_channels, dropout=0.3):
        super().__init__()

        # 调整后的多尺度卷积分支 (5->3->1)
        self.conv_layers = nn.ModuleList([
            # 5x1卷积组
            nn.Sequential(
                nn.Conv2d(in_channels, out_channels, (5, 1), padding=(2, 0)),
                nn.BatchNorm2d(out_channels),
                nn.ELU(),
                nn.Conv2d(out_channels, out_channels, (5, 1), padding=(2, 0)),
                nn.BatchNorm2d(out_channels),
                nn.ELU(),
                nn.Dropout2d(dropout)
            ),
            # 3x1卷积组
            nn.Sequential(
                nn.Conv2d(out_channels, out_channels, (3, 1), padding=(1, 0)),
                nn.BatchNorm2d(out_channels),
                nn.ELU(),
                nn.Conv2d(out_channels, out_channels, (3, 1), padding=(1, 0)),
                nn.BatchNorm2d(out_channels),
                nn.ELU(),
                nn.Dropout2d(dropout)
            ),
            # 1x1卷积组
            nn.Sequential(
                nn.Conv2d(out_channels, out_channels, (1, 1)),
                nn.BatchNorm2d(out_channels),
                nn.ELU(),
                nn.Conv2d(out_channels, out_channels, (1, 1)),
                nn.BatchNorm2d(out_channels),
                nn.ELU(),
                nn.Dropout2d(dropout)
            )
        ])

        # 保持跳跃连接不变
        self.shortcut = nn.Sequential()
        if in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, (1, 1)),
                nn.BatchNorm2d(out_channels)
            )

        # 最终激活
        self.final_activation = nn.ELU()

    def forward(self, x):
        identity = self.shortcut(x)

        for layer in self.conv_layers:
            x = layer(x)

        return self.final_activation(x + identity)


class SelfAttention(nn.Module):
    def __init__(self, in_channels, num_heads=8, dropout=0.1):
        super().__init__()
        self.num_heads = num_heads
        self.head_dim = in_channels // num_heads
        self.scale = self.head_dim ** -0.5

        self.w_q = nn.Linear(in_channels, in_channels)
        self.w_k = nn.Linear(in_channels, in_channels)
        self.w_v = nn.Linear(in_channels, in_channels)

        self.proj = nn.Conv1d(in_channels, in_channels, kernel_size=1)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        # x: [B, C, T]
        B, C, T = x.shape
        identity = x  # 保存输入用于残差连接

        x_perm = x.permute(0, 2, 1)  # [B, T, C]
        q = self.w_q(x_perm).permute(0, 2, 1)  # [B, C, T]
        k = self.w_k(x_perm).permute(0, 2, 1)
        v = self.w_v(x_perm).permute(0, 2, 1)
        q = q.reshape(B, self.num_heads, self.head_dim, T).permute(0, 1, 3, 2)
        k = k.reshape(B, self.num_heads, self.head_dim, T).permute(0, 1, 3, 2)
        v = v.reshape(B, self.num_heads, self.head_dim, T).permute(0, 1, 3, 2)

        q = torch.nn.functional.normalize(q, dim=-1)
        k = torch.nn.functional.normalize(k, dim=-1)

        # 计算注意力
        attn = (q @ k.transpose(-2, -1)) * self.scale  # 或使用 / math.sqrt(self.head_dim)
        attn = attn.softmax(-1)
        attn = self.dropout(attn)  # 添加dropout

        out = (attn @ v)  # [B, heads, T, head_dim]
        out = out.permute(0, 1, 3, 2).reshape(B, C, T)
        out = self.proj(out)
        out = self.dropout(out)  # 添加dropout

        return identity + out

