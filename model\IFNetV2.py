# --------------------------------------------------------
# IFNet
# Written by <PERSON><PERSON><PERSON>
# --------------------------------------------------------

import math
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from timm.models.layers import trunc_normal_

class Conv(nn.Module):
    def __init__(self, conv, activation=None, bn=None):
        nn.Module.__init__(self)
        self.conv = conv
        self.activation = activation
        if bn:
            self.conv.bias = None
        self.bn = bn

    def forward(self, x):
        x = self.conv(x)
        if self.bn:
            x = self.bn(x)
        if self.activation:
            x = self.activation(x)
        return x


class LogPowerLayer(nn.Module):
    def __init__(self, dim):
        super(Log<PERSON><PERSON><PERSON><PERSON>ay<PERSON>, self).__init__()
        self.dim = dim

    def forward(self, x):
        return torch.log(torch.clamp(torch.mean(x ** 2, dim=self.dim), 1e-4, 1e4))
        #return torch.log(torch.clamp(x.var(dim=self.dim, keepdim=False), 1e-4, 1e4))


class InterFre(nn.Module):
    def __init__(self):
        nn.Module.__init__(self)

    def forward(self, x):
        out = sum(x)
        out = F.gelu(out)
        return out


class Conv1dWithConstraint(nn.Conv1d):
    def __init__(self, *args, doWeightNorm = True, max_norm=0.5, **kwargs):
        self.max_norm = max_norm
        self.doWeightNorm = doWeightNorm
        super(Conv1dWithConstraint, self).__init__(*args, **kwargs)

    def forward(self, x):
        if self.doWeightNorm:
            self.weight.data = torch.renorm(
                self.weight.data, p=2, dim=0, maxnorm=self.max_norm
            )
        return super(Conv1dWithConstraint, self).forward(x)


class LinearWithConstraint(nn.Linear):
    def __init__(self, *args, doWeightNorm=True, max_norm=0.5, **kwargs):
        self.max_norm = max_norm
        self.doWeightNorm = doWeightNorm
        super(LinearWithConstraint, self).__init__(*args, **kwargs)

    def forward(self, x):
        if self.doWeightNorm:
            self.weight.data = torch.renorm(
                self.weight.data, p=2, dim=0, maxnorm=self.max_norm
            )
        return super(LinearWithConstraint, self).forward(x)


class Stem(nn.Module):
    def __init__(self, in_planes, out_planes = 64, kernel_size = 63, patch_size = 125, radix = 2):
        nn.Module.__init__(self)
        self.in_planes = in_planes
        self.out_planes = out_planes
        self.mid_planes = out_planes * radix
        self.kernel_size = kernel_size
        self.radix = radix
        self.patch_size = patch_size

        self.sconv = Conv(nn.Conv1d(self.in_planes, self.mid_planes, 1, bias=False, groups = radix),
                          bn=nn.BatchNorm1d(self.mid_planes), activation=None)

        self.tconv = nn.ModuleList()
        for _ in range(self.radix):
            self.tconv.append(Conv(nn.Conv1d(self.out_planes, self.out_planes, kernel_size, 1, groups=self.out_planes, padding=kernel_size // 2, bias=False,),
                                   bn=nn.BatchNorm1d(self.out_planes), activation=None))
            kernel_size //= 2

        self.interFre = InterFre()

        self.power = LogPowerLayer(dim=3)
        self.dp = nn.Dropout(0.5)

    def forward(self, x):
        N, C, T = x.shape
        out = self.sconv(x)

        out = torch.split(out, self.out_planes, dim=1)
        out = [m(x) for x, m in zip(out, self.tconv)]

        out = self.interFre(out)
        out = out.reshape(N, self.out_planes, T // self.patch_size, self.patch_size)
        out = self.power(out)
        out = self.dp(out)
        return out


class IFNet(nn.Module):
    def __init__(self, in_planes, out_planes, kernel_size, radix, patch_size):
        r'''Interactive Frequency Convolutional Neural Network V2

        :param in_planes: Number of input EEG channels
        :param out_planes: Number of output feature dimensions
        :param kernel_size: Temporal convolution kernel size
        :param radix:   Number of input frequency bands
        :param patch_size: Temporal pooling size
        :param time_points: Input window length
        :param num_classes: Number of classes
        '''
        nn.Module.__init__(self)
        self.in_planes = in_planes * radix
        self.out_planes = out_planes
        self.stem = Stem(self.in_planes, self.out_planes, kernel_size, patch_size=patch_size, radix=radix)

        # self.fc = nn.Sequential(
        #     LinearWithConstraint(out_planes * (time_points // patch_size), num_classes, doWeightNorm=True),
        # )
        #print(f'fc layer feature dims:{self.fc[-1].weight.shape}')
        self.apply(self.initParms)

    def initParms(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.01)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, (nn.LayerNorm, nn.BatchNorm1d, nn.BatchNorm2d)):
            if m.weight is not None:
                nn.init.constant_(m.weight, 1.0)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, (nn.Conv1d, nn.Conv2d)):
            trunc_normal_(m.weight, std=.01)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        x = torch.squeeze(x, dim=1)

        # 检查输入通道数是否匹配期望
        expected_channels = self.in_planes  # in_planes * radix
        if x.shape[1] != expected_channels:
            # 如果输入通道数不匹配，尝试调整
            if x.shape[1] == expected_channels // 2:
                # 如果输入通道数是期望的一半，可能需要复制或填充
                x = x.repeat(1, 2, 1)
            else:
                raise RuntimeError(f"Input channel mismatch: got {x.shape[1]}, expected {expected_channels}")

        x = x[:, :, :750]

        out = self.stem(x)
        # out = self.fc(out.flatten(1))
        x = torch.flatten(out, start_dim=1)
        return x

class classifier(nn.Module):
    def __init__(self, num_classes, input_features=64*6):
        super(classifier, self).__init__()

        self.dense = nn.Sequential(
            LinearWithConstraint(input_features, num_classes, max_norm=0.5),
            nn.LogSoftmax(dim=1)

        )

    def forward(self, x):
        x = self.dense(x)
        return x


class Net(nn.Module):
    def __init__(self,
                 num_classes: int,
                 num_channels: int,
                 sampling_rate: int,
                 radix: int = 2):
        super(Net, self).__init__()

        self.backbone = IFNet(in_planes=num_channels, out_planes=64, kernel_size=63, radix=radix, patch_size=125)

        # 更新分类器的输入维度：out_planes * (time_points // patch_size)
        # time_points = 750, patch_size = 125, 所以 750 // 125 = 6
        classifier_input_features = 64 * 6
        self.classifier = classifier(num_classes, input_features=classifier_input_features)

    def forward(self, x):
        output = self.backbone(x)
        x = self.classifier(output)
        return x


def get_model(args):
    # 根据filter_bank设置确定radix参数
    radix = 2  # 默认值
    if hasattr(args, 'filter_bank') and args.filter_bank:
        if hasattr(args, 'bank') and args.bank:
            radix = len(args.bank)
        else:
            radix = 2  # 默认频率带数
    else:
        radix = 1  # 没有filter bank时为1

    model = Net(num_classes=args.num_classes,
                num_channels=args.num_channels,
                sampling_rate=args.sampling_rate,
                radix=radix)

    return model