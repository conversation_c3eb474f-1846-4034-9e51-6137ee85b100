args: !!python/object/new:easydict.EasyDict
  dictitems:
    BASE_PATH: ./Datasets/KU_MI/
    CKPT_PATH: ./checkpoints
    DATA_PATH: E:\Project\MASSA\Datasets\KU_MI
    EPOCHS: 100
    GPU_NUM: '0'
    LOG_NAME: ********_task_KU_MI_batch_16_lr_1e-3_window_3_MASSANet100-1
    LOG_PATH: ./logs
    SEED: 42
    bank: &id001
    - - - 4
        - 16
    - - - 16
        - 40
    batch_size: 16
    current_time: '********'
    device: &id002 !!python/object/apply:torch.device
    - cuda
    downsampling: 250
    filter_bank: true
    is_test: false
    k_folds: 5
    kernel_num: 8
    log_etc: MASSANet100-1
    lr: 0.001
    momentum: 0.9
    num_channels: 20
    num_classes: 2
    num_subjects: 10
    num_workers: 0
    sampling_rate: 250
    target_subject: 9
    task: KU_MI
    weight_decay: 0.075
    window_length: 3
  state:
    BASE_PATH: ./Datasets/KU_MI/
    CKPT_PATH: ./checkpoints
    DATA_PATH: E:\Project\MASSA\Datasets\KU_MI
    EPOCHS: 100
    GPU_NUM: '0'
    LOG_NAME: ********_task_KU_MI_batch_16_lr_1e-3_window_3_MASSANet100-1
    LOG_PATH: ./logs
    SEED: 42
    bank: *id001
    batch_size: 16
    current_time: '********'
    device: *id002
    downsampling: 250
    filter_bank: true
    is_test: false
    k_folds: 5
    kernel_num: 8
    log_etc: MASSANet100-1
    lr: 0.001
    momentum: 0.9
    num_channels: 20
    num_classes: 2
    num_subjects: 10
    num_workers: 0
    sampling_rate: 250
    target_subject: 9
    task: KU_MI
    weight_decay: 0.075
    window_length: 3
