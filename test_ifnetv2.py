#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import torch
import numpy as np
from easydict import EasyDict

# 模拟配置
args = EasyDict({
    'num_classes': 4,
    'num_channels': 3,
    'sampling_rate': 250,
    'filter_bank': True,
    'bank': [[[4, 16]], [[16, 40]]]  # 2个频率带
})

def test_ifnetv2():
    """测试IFNetV2模型的输入输出"""
    
    try:
        from model.IFNetV2 import get_model
        
        print("Testing IFNetV2 model...")
        print(f"Config: {args}")
        
        # 创建模型
        model = get_model(args)
        print(f"Model created successfully")
        
        # 创建测试数据
        # 根据filter bank，输入应该是 [batch, 1, num_channels * radix, time]
        radix = len(args.bank)
        batch_size = 2
        time_points = 750
        
        # 模拟filter bank处理后的数据
        input_channels = args.num_channels * radix  # 3 * 2 = 6
        test_input = torch.randn(batch_size, 1, input_channels, time_points)
        
        print(f"Test input shape: {test_input.shape}")
        print(f"Expected: [batch={batch_size}, 1, channels={input_channels}, time={time_points}]")
        
        # 前向传播
        model.eval()
        with torch.no_grad():
            output = model(test_input)
            print(f"Output shape: {output.shape}")
            print(f"Expected: [batch={batch_size}, num_classes={args.num_classes}]")
            
        print("✓ IFNetV2 test passed!")
        return True
        
    except Exception as e:
        print(f"✗ IFNetV2 test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_ifnetv2()
