import torch
import torch.nn as nn
import torch.nn.functional as F
from sqlalchemy.testing.provision import drop_db
from torch.nn import Module, Conv2d, Parameter, Softmax
import torch
import torch.nn.functional as F
from model.layers import (Conv2dWithConstraint, UnifiedAttention, FrequencyBandAttention, TemporalSelfAttention,SelfAttention,
                          MultiScaleSpectralAttention, EEGResidualBlock1, EEGResidualBlock, LazyLinearWithConstraint)


class MASSA(nn.Module):
    def __init__(self, num_channels, sampling_rate, F1=16, D=1, F2='auto', num_bands=6, drop_out=0.3, ):
        super().__init__()
        F2 = F1 * D  if F2 == 'auto' else F2

        # 1. 前置频率带注意力模块 - 增强正则化
        self.freq_att = FrequencyBandAttention(num_bands=num_bands)

        # 2. 双路频谱特征提取 - 增加dropout防止过拟合
        self.spectral_branch = nn.ModuleList([
            nn.Sequential(
                # MultiScaleSpectralAttention(1, F1=F1, scales=[128, 64]),
                nn.Conv2d(1, F1, (1, 125), padding='same',),
                nn.BatchNorm2d(F1),
                MultiScaleSpectralAttention(F1, F1=F1, scales=[128, 64]),
                # nn.ELU(),
                # nn.MaxPool2d((1, 32), stride=32),
                # EEGResidualBlock(F1, F1)
            ),
            nn.Sequential(
                # MultiScaleSpectralAttention(1, F1=F1, scales=[32, 2]),
                nn.Conv2d(1, F1, (1, 30), padding='same', ),  # 降低权重范数约束
                nn.BatchNorm2d(F1),
                MultiScaleSpectralAttention(F1, F1=F1, scales=[16, 2]),
                # nn.ELU(),
                # nn.MaxPool2d((1, 32), stride=32),
                # EEGResidualBlock(F1, F1)
            )
        ])

        # 3. 空间特征分支 - 增加正则化强度
        self.spatial_branch = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(F1, F1, (num_channels, 1), groups=F1 // 4),  # 分组卷积增强局部交互
                nn.BatchNorm2d(F1),
                nn.ELU(),
                nn.MaxPool2d((1, 32), stride=32),
                nn.Dropout(drop_out),
                nn.Conv2d(F1, F2, (1, 1)),
                UnifiedAttention(mode='s', in_channels=F1, ),
                nn.Dropout(drop_out),
                EEGResidualBlock(F2, F2),

            ),
            nn.Sequential(
                nn.Conv2d(F1, F2, (num_channels, 1)),
                nn.BatchNorm2d(F2),
                nn.ELU(),
                nn.MaxPool2d((1, 35), stride=16),
                nn.Dropout(drop_out),
                nn.Conv2d(F2, F2, (1, 1)),
                UnifiedAttention(mode='s', in_channels=F2, ),
                nn.Dropout(drop_out),
                EEGResidualBlock(F2, F2),

            )
        ])

        self.attn = UnifiedAttention( mode='m', in_channels=F2 * 2, num_heads=8, num_bands=num_bands)
        self.pool = nn.AdaptiveMaxPool2d((1, 35))
        self.drop = nn.Dropout(drop_out)
        self.final_bn = nn.BatchNorm2d(F2 * 2)
        # self.temp_att = SelfAttention(in_channels=F2 * 2, num_heads=8, dropout=0.3,)
        self.temp_att = TemporalSelfAttention(in_channels=F2 * 2, num_heads=8, dropout=0.2)


    def forward(self, x):
        # ---- 频率注意力 ----
        # x = self.freq_att(x)  # [B,1,C,T]

        # ---- 双路频谱特征 ----
        x1 = self.spectral_branch[0](x)
        x2 = self.spectral_branch[1](x)

        # x1 = self.freq_att(x1)
        # x2 = self.freq_att(x2)

        # ---- 空间特征提取 ----
        s1 = self.spatial_branch[0](x1)  # [B,F2,1,T1]
        s2 = self.spatial_branch[1](x2)  # [B,F2,1,T2]

        # ---- 时序对齐 ----
        s1 = self.pool(s1)
        s2 = self.pool(s2)


        # ---- 特征融合 ----
        fused = torch.cat([s1, s2], dim=1)  # [B, 2*F2, 1, T]
        fused = self.final_bn(fused)
        attn_out = self.attn(fused)
        # fused = fused + self.drop(attn_out)

        # ---- 新增时域自注意力 ----
        # 先 squeeze 出时间维度: [B, C, 1, T] -> [B, C, T]
        B, C, _, T = fused.shape
        tmp = fused.view(B, C, T)
        # tmp = self.final_bn(tmp)
        tmp = self.temp_att(tmp)  # [B, C, T]
        tmp = tmp.view(B, C, 1, T)

        # ---- 残差连接 ----
        # return fused
        return attn_out + self.drop(tmp)


class classifier(nn.Module):
    def __init__(self, num_classes):
        super(classifier, self).__init__()

        self.dense = nn.Sequential(
            nn.Conv2d(32, 16, (1, 35)),
            nn.BatchNorm2d(16),
            # nn.ELU(),
            nn.Conv2d(16, num_classes, (1, 1)),
            nn.LogSoftmax(dim=1)
        )

    def forward(self, x):
        x= self.dense(x)
        x = torch.squeeze(x, 3)
        x = torch.squeeze(x, 2)
        return x


class classifier1(nn.Module):
    def __init__(self, num_classes):
        super(classifier, self).__init__()

        # Enhanced classifier with attention and more layers
        self.attention = nn.Sequential(
            nn.Conv2d(144, 144, (1, 1)),
            nn.Sigmoid()
        )

        self.dense = nn.Sequential(
            nn.Conv2d(144, 64, (1, 75)),
            nn.ELU(),
            nn.BatchNorm2d(64),
            nn.Dropout(0.3),
            nn.Conv2d(64, 32, (1, 1)),
            nn.ELU(),
            nn.BatchNorm2d(32),
            nn.Dropout(0.3),
            nn.Conv2d(32, num_classes, (1, 1)),
            nn.LogSoftmax(dim=1)
        )

    def forward(self, x):
        # Apply attention
        att = self.attention(x)
        x = x * att

        # Apply dense layers
        x = self.dense(x)
        x = torch.squeeze(x, 3)
        x = torch.squeeze(x, 2)
        return x

class Net(nn.Module):
    def __init__(self,
                 num_classes: 4,
                 num_channels: int,
                 sampling_rate: int):
        super(Net, self).__init__()

        self.backbone = MASSA(num_channels=num_channels, sampling_rate=sampling_rate)

        self.classifier = classifier(num_classes)

    def forward(self, x):
        x = x.float()
        x = self.backbone(x)
        x = self.classifier(x)
        return x


def get_model(args):
    model = Net(num_classes=args.num_classes,
                num_channels=args.num_channels,
                sampling_rate=args.sampling_rate)

    return model


class ActSquare(nn.Module):
    def __init__(self):
        super(ActSquare, self).__init__()
        pass

    def forward(self, x):
        return torch.square(x)

class ActLog(nn.Module):
    def __init__(self, eps=1e-06):
        super(ActLog, self).__init__()
        self.eps = eps

    def forward(self, x):
        return torch.log(torch.clamp(x, min=self.eps))