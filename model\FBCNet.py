import torch
import torch.nn as nn
from model.layers import Conv2dWithConstraint, LinearWithConstraint
from torch.nn import functional as F
class FBCNet(nn.Module):
    def __init__(
            self,
            num_channels: int,
            m=32,
            n_band=None,  # 改为None，自动推断
            temporal_stride=4,
            input_channels=None,  # 添加输入通道数参数
    ):
        super(FBCNet, self).__init__()
        self.temporal_stride = temporal_stride

        # 如果没有指定n_band，根据输入通道数自动设置
        if n_band is None:
            if input_channels is not None:
                n_band = input_channels
            else:
                n_band = 9  # 默认值

        self.n_band = n_band

        # SCB (Spatial Convolution Block)
        self.scb = nn.Sequential(
            Conv2dWithConstraint(n_band, m * n_band, (num_channels, 1), groups=n_band, max_norm=2),
            nn.BatchNorm2d(m * n_band),
            Swish()
        )

        # Temporal Layer
        self.temporal_layer = LogVarLayer(-1)


    def forward(self, x):
        # 调试信息
        # print(f"FBCNet input shape: {x.shape}")

        x = torch.squeeze(x)
        # print(f"After squeeze: {x.shape}")

        # 如果输入是 [batch, channels*n_band, time] 格式，需要重塑为 [batch, n_band, channels, time]
        if len(x.shape) == 3 and x.shape[1] % self.n_band == 0:
            batch_size, total_channels, time_points = x.shape
            channels_per_band = total_channels // self.n_band
            x = x.reshape(batch_size, self.n_band, channels_per_band, time_points)
            # print(f"Reshaped input from {(batch_size, total_channels, time_points)} to {x.shape}")

        # 检查输入通道数是否匹配
        if x.shape[1] != self.n_band:
            raise RuntimeError(f"Input has {x.shape[1]} frequency bands, but FBCNet expects {self.n_band} bands. "
                             f"Input shape: {x.shape}, Expected: [batch, {self.n_band}, channels, time]")

        x = self.scb(x)
        x = F.pad(x, (0, 1))
        x = x.reshape([*x.shape[:2], self.temporal_stride, int(x.shape[-1] / self.temporal_stride)])
        x = self.temporal_layer(x)
        x = torch.flatten(x, start_dim=1)
        return x


class classifier(nn.Module):
    def __init__(self, num_classes, input_features=32*9*4):
        super(classifier, self).__init__()

        self.dense = nn.Sequential(
            LinearWithConstraint(input_features, num_classes, max_norm=0.5),
            nn.LogSoftmax(dim=1)

        )

    def forward(self, x):
        x = self.dense(x)
        return x


class Net(nn.Module):
    def __init__(self,
                 num_classes: int,
                 num_channels: int,
                 sampling_rate: int,
                 input_channels: int = None):
        super(Net, self).__init__()

        # 如果没有指定input_channels，使用默认值
        if input_channels is None:
            input_channels = 9  # FBCNet的默认频率带数

        self.backbone = FBCNet(num_channels=num_channels, input_channels=input_channels)

        # 计算分类器的输入维度：m * n_band * temporal_stride
        m = 32  # FBCNet中的m参数
        temporal_stride = 4  # FBCNet中的temporal_stride参数
        classifier_input_features = m * input_channels * temporal_stride

        self.classifier = classifier(num_classes, input_features=classifier_input_features)

    def forward(self, x):
        output = self.backbone(x)
        x = self.classifier(output)
        return x


def get_model(args):
    # 根据filter_bank设置确定输入通道数
    input_channels = None
    if hasattr(args, 'filter_bank') and args.filter_bank:
        if hasattr(args, 'bank') and args.bank:
            # 对于KUMI数据集的filter bank处理方式：
            # 数据形状从 [batch, 1, 20, time] 变为 [batch, 1, 40, time]
            # 我们需要重新整形为 [batch, 2, 20, time] 以匹配FBCNet的期望
            input_channels = len(args.bank)
            # print(f"Filter bank enabled with {input_channels} frequency bands")
            # print(f"Expected data shape after preprocessing: [batch, 1, {len(args.bank) * args.num_channels}, time]")
            # print(f"FBCNet expects: [batch, {len(args.bank)}, {args.num_channels}, time]")
        else:
            input_channels = 9  # 默认频率带数
    else:
        input_channels = 1  # 没有filter bank时为1

    print(f"FBCNet will be initialized with {input_channels} input channels")

    model = Net(num_classes=args.num_classes,
                num_channels=args.num_channels,
                sampling_rate=args.sampling_rate,
                input_channels=input_channels)

    return model

class ActSquare(nn.Module):
    def __init__(self):
        super(ActSquare, self).__init__()
        pass

    def forward(self, x):
        return torch.square(x)


class ActLog(nn.Module):
    def __init__(self, eps=1e-06):
        super(ActLog, self).__init__()
        self.eps = eps

    def forward(self, x):
        return torch.log(torch.clamp(x, min=self.eps))

class Swish(nn.Module):
    '''
    The swish layer: implements the swish activation function
    '''

    def __init__(self):
        super(Swish, self).__init__()

    def forward(self, x):
        return x * torch.sigmoid(x)


class LogVarLayer(nn.Module):
    '''
    The log variance layer: calculates the log variance of the data along given 'dim'
    (natural logarithm)
    '''

    def __init__(self, dim):
        super(LogVarLayer, self).__init__()
        self.dim = dim

    def forward(self, x):
        return torch.log(torch.clamp(x.var(dim=self.dim, keepdim=True), 1e-6, 1e6))
